/* Apple-Inspired Design System for UUID Management */

/* ===== CSS VARIABLES (Apple Design Tokens) ===== */
:root {
  /* Typography - San Francisco Font System */
  --font-family-system: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  /* Colors - Apple's Modern Palette */
  --color-white: #FFFFFF;
  --color-black: #000000;
  
  /* Grays */
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F5F5F7;
  --color-gray-200: #F2F2F7;
  --color-gray-300: #E5E5EA;
  --color-gray-400: #D1D1D6;
  --color-gray-500: #8E8E93;
  --color-gray-600: #636366;
  --color-gray-700: #48484A;
  --color-gray-800: #2C2C2E;
  --color-gray-900: #1C1C1E;
  
  /* SF Colors */
  --color-blue: #007AFF;
  --color-blue-dark: #0056CC;
  --color-green: #34C759;
  --color-orange: #FF9500;
  --color-red: #FF3B30;
  --color-purple: #AF52DE;
  --color-yellow: #FFCC00;
  
  /* Semantic Colors */
  --color-primary: var(--color-blue);
  --color-primary-hover: var(--color-blue-dark);
  --color-success: var(--color-green);
  --color-warning: var(--color-orange);
  --color-danger: var(--color-red);
  
  /* Background Colors */
  --color-bg-primary: var(--color-white);
  --color-bg-secondary: var(--color-gray-100);
  --color-bg-tertiary: var(--color-gray-200);
  
  /* Text Colors */
  --color-text-primary: var(--color-black);
  --color-text-secondary: var(--color-gray-600);
  --color-text-tertiary: var(--color-gray-500);
  
  /* Spacing - Apple's 8pt Grid System */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 9999px;
  
  /* Shadows - Apple's Subtle Depth */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.12);
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family-system);
  font-weight: var(--font-weight-regular);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  margin: 0;
  padding: 0;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  margin: 0 0 var(--spacing-md) 0;
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--color-text-secondary);
}

small {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

code {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  background-color: var(--color-gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  color: var(--color-text-primary);
}

/* ===== LAYOUT COMPONENTS ===== */
.container-apple {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.page-header {
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-lg) 0;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.page-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

/* ===== CARD COMPONENTS ===== */
.card-apple {
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card-apple:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-apple-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-bg-secondary);
}

.card-apple-body {
  padding: var(--spacing-lg);
}

.card-apple-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-bg-secondary);
}

/* ===== BUTTON COMPONENTS ===== */
.btn-apple {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-family-system);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px; /* Apple's minimum touch target */
  gap: var(--spacing-sm);
}

.btn-apple:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn-apple:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-apple-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-apple-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-apple-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-text-primary);
}

.btn-apple-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
}

.btn-apple-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-apple-outline:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-apple-ghost {
  background-color: transparent;
  color: var(--color-text-secondary);
}

.btn-apple-ghost:hover:not(:disabled) {
  background-color: var(--color-gray-100);
  color: var(--color-text-primary);
}

/* Button Sizes */
.btn-apple-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-apple-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* ===== FORM COMPONENTS ===== */
.form-group-apple {
  margin-bottom: var(--spacing-lg);
}

.form-label-apple {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-input-apple {
  width: 100%;
  padding: var(--spacing-md);
  font-family: var(--font-family-system);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  min-height: 44px;
}

.form-input-apple:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-input-apple::placeholder {
  color: var(--color-text-tertiary);
}

/* ===== BADGE COMPONENTS ===== */
.badge-apple {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border-radius: var(--radius-full);
  gap: var(--spacing-xs);
}

.badge-apple-primary { background-color: var(--color-primary); color: var(--color-white); }
.badge-apple-success { background-color: var(--color-success); color: var(--color-white); }
.badge-apple-warning { background-color: var(--color-warning); color: var(--color-white); }
.badge-apple-danger { background-color: var(--color-danger); color: var(--color-white); }
.badge-apple-secondary { background-color: var(--color-gray-200); color: var(--color-text-primary); }

/* ===== NAVIGATION COMPONENTS ===== */
.sidebar-apple {
  width: 280px;
  height: 100vh;
  background-color: var(--color-bg-primary);
  border-right: 1px solid var(--color-gray-200);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  overflow-y: auto;
  transition: transform var(--transition-base);
}

.sidebar-apple-header {
  padding: var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--color-gray-200);
}

.sidebar-apple-brand {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.sidebar-apple-nav {
  padding: var(--spacing-lg);
}

.sidebar-apple-nav-item {
  margin-bottom: var(--spacing-xs);
}

.sidebar-apple-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

.sidebar-apple-nav-link:hover {
  background-color: var(--color-gray-100);
  color: var(--color-text-primary);
}

.sidebar-apple-nav-link.active {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.main-content-apple {
  margin-left: 280px;
  min-height: 100vh;
  background-color: var(--color-bg-secondary);
  transition: margin-left var(--transition-base);
}

/* ===== TABLE COMPONENTS ===== */
.table-apple {
  width: 100%;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.table-apple thead {
  background-color: var(--color-bg-secondary);
}

.table-apple th {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200);
}

.table-apple td {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-gray-100);
  vertical-align: middle;
}

.table-apple tbody tr:hover {
  background-color: var(--color-gray-50);
}

.table-apple tbody tr:last-child td {
  border-bottom: none;
}

/* ===== MODAL COMPONENTS ===== */
.modal-apple {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
}

.modal-apple.show {
  opacity: 1;
  visibility: visible;
}

.modal-apple-content {
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-base);
}

.modal-apple.show .modal-apple-content {
  transform: scale(1) translateY(0);
}

.modal-apple-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-apple-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.modal-apple-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.modal-apple-close:hover {
  background-color: var(--color-gray-100);
  color: var(--color-text-primary);
}

.modal-apple-body {
  padding: var(--spacing-lg);
}

.modal-apple-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* ===== LOADING COMPONENTS ===== */
.loading-apple {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.skeleton-apple {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== UUID DISPLAY COMPONENTS ===== */
.uuid-display {
  font-family: var(--font-family-mono);
  background: var(--color-gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  max-width: 200px;
}

.uuid-display:hover {
  background: var(--color-gray-200);
  transform: translateY(-1px);
}

.uuid-display.expanded {
  max-width: none;
}

.uuid-display .uuid-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uuid-display.expanded .uuid-text {
  white-space: normal;
  word-break: break-all;
}

.uuid-display .copy-icon {
  opacity: 0;
  transition: opacity var(--transition-fast);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

.uuid-display:hover .copy-icon {
  opacity: 1;
}

.uuid-display .expand-icon {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  transition: transform var(--transition-fast);
}

.uuid-display.expanded .expand-icon {
  transform: rotate(180deg);
}

/* UUID Card Row */
.uuid-card-row {
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-fast);
  position: relative;
}

.uuid-card-row:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--color-gray-300);
}

.uuid-card-row:last-child {
  margin-bottom: 0;
}

.uuid-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-md);
}

.uuid-card-main {
  flex: 1;
  min-width: 0;
}

.uuid-card-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.uuid-card-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-gray-200);
}

.uuid-meta-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.uuid-meta-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.uuid-meta-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Status Badge Group */
.status-badge-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  align-items: center;
}

.status-badge-primary {
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-badge-success {
  background: var(--color-success);
  color: var(--color-white);
}

.status-badge-warning {
  background: var(--color-orange);
  color: var(--color-white);
}

.status-badge-danger {
  background: var(--color-danger);
  color: var(--color-white);
}

.status-badge-secondary {
  background: var(--color-gray-200);
  color: var(--color-text-primary);
}

/* Action Button Group */
.action-button-group {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  border: none;
  background: var(--color-gray-100);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.action-btn:hover {
  background: var(--color-gray-200);
  color: var(--color-text-primary);
  transform: translateY(-1px);
}

.action-btn.primary {
  background: var(--color-primary);
  color: var(--color-white);
}

.action-btn.primary:hover {
  background: var(--color-primary-hover);
}

.action-btn.success {
  background: var(--color-success);
  color: var(--color-white);
}

.action-btn.success:hover {
  background: #28A745;
}

.action-btn.danger {
  background: transparent;
  color: var(--color-danger);
}

.action-btn.danger:hover {
  background: rgba(255, 59, 48, 0.1);
  color: var(--color-danger);
}

/* Recent UUIDs Card Layout */
.recent-uuid-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-sm);
}

.recent-uuid-card:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--color-gray-300);
  transform: translateY(-1px);
}

.recent-uuid-card:last-child {
  margin-bottom: 0;
}

.recent-uuid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.recent-uuid-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--color-gray-100);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl);
  color: var(--color-text-secondary);
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.3;
  background: linear-gradient(135deg, var(--color-gray-300) 0%, var(--color-gray-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.empty-state-description {
  font-size: var(--font-size-base);
  color: var(--color-text-tertiary);
  margin-bottom: var(--spacing-xl);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* ===== UTILITY CLASSES ===== */
.text-apple-primary { color: var(--color-text-primary); }
.text-apple-secondary { color: var(--color-text-secondary); }
.text-apple-tertiary { color: var(--color-text-tertiary); }

.bg-apple-primary { background-color: var(--color-bg-primary); }
.bg-apple-secondary { background-color: var(--color-bg-secondary); }
.bg-apple-tertiary { background-color: var(--color-bg-tertiary); }

.shadow-apple-sm { box-shadow: var(--shadow-sm); }
.shadow-apple-md { box-shadow: var(--shadow-md); }
.shadow-apple-lg { box-shadow: var(--shadow-lg); }

.rounded-apple-sm { border-radius: var(--radius-sm); }
.rounded-apple-md { border-radius: var(--radius-md); }
.rounded-apple-lg { border-radius: var(--radius-lg); }
.rounded-apple-xl { border-radius: var(--radius-xl); }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .uuid-card-meta {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .uuid-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .uuid-card-actions {
    justify-content: center;
  }

  .uuid-display {
    max-width: 150px;
  }

  .action-button-group {
    justify-content: center;
    flex-wrap: wrap;
  }

  .recent-uuid-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xs);
  }

  .recent-uuid-footer {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xs);
  }
}

/* ===== ANIMATIONS ===== */
@keyframes uuid-copy-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.uuid-copy-success {
  animation: uuid-copy-success 0.3s ease-in-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.4s ease-out;
}

/* ===== SEARCH AND FILTER ENHANCEMENTS ===== */
.search-highlight {
  background: rgba(255, 214, 10, 0.3);
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: var(--font-weight-semibold);
}

.filter-active {
  background: var(--color-primary) !important;
  color: var(--color-white) !important;
}

.sort-indicator {
  margin-left: var(--spacing-xs);
  opacity: 0.5;
  transition: opacity var(--transition-fast);
}

.sort-indicator.active {
  opacity: 1;
  color: var(--color-primary);
}

/* ===== LOADING STATES ===== */
.uuid-skeleton {
  background: linear-gradient(90deg, var(--color-gray-100) 25%, var(--color-gray-200) 50%, var(--color-gray-100) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-sm);
  height: 20px;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
  z-index: 10;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-gray-300);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== REAL-TIME UPDATE INDICATORS ===== */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-success);
  animation: pulse 2s infinite;
}

.status-indicator.updating {
  background: var(--color-orange);
  animation: pulse 1s infinite;
}

.status-indicator.error {
  background: var(--color-danger);
  animation: none;
}

.usage-count-updated {
  animation: usage-update-flash 0.6s ease-out;
}

@keyframes usage-update-flash {
  0% {
    background: var(--color-primary);
    color: var(--color-white);
    transform: scale(1);
  }
  50% {
    background: var(--color-primary);
    color: var(--color-white);
    transform: scale(1.1);
  }
  100% {
    background: var(--color-gray-100);
    color: var(--color-text-primary);
    transform: scale(1);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  :root {
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
  }

  .sidebar-apple {
    transform: translateX(-100%);
  }

  .sidebar-apple.show {
    transform: translateX(0);
  }

  .main-content-apple {
    margin-left: 0;
  }

  .container-apple {
    padding: 0 var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-md) 0;
  }

  .card-apple-body,
  .card-apple-header,
  .card-apple-footer {
    padding: var(--spacing-md);
  }

  .modal-apple-content {
    margin: var(--spacing-md);
    width: calc(100% - 2rem);
  }

  .table-apple {
    font-size: var(--font-size-xs);
  }

  .table-apple th,
  .table-apple td {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
